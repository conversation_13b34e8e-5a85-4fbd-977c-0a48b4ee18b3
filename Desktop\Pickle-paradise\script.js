document.addEventListener('DOMContentLoaded', () => {
    const profileAvatar = document.querySelector('.profile-avatar');
    const profileMenu = document.querySelector('.profile-menu');
    const closeMenu = document.querySelector('.close-menu');
    const postNav = document.querySelector('.post-nav');
    const postDropdown = document.querySelector('.small-dropdown');
    const postDropdownMenu = document.querySelector('.post-dropdown-menu');
    const closePostDropdown = document.querySelector('.close-post-dropdown');

   const storyCarousel = document.querySelector('.story-carousel');
   const storyCards = document.querySelectorAll('.story-card');
   const previousButton = document.querySelector('.previous-button');
   const nextButton = document.querySelector('.next-button');

   let currentIndex = 0;

   // Get the actual distance to move (card width + gap)
   const cardWidth = storyCards[0].offsetWidth;
   const computedStyle = window.getComputedStyle(storyCarousel);
   const gap = parseInt(computedStyle.gap) || 10; // fallback to 10px if gap not found
   const moveDistance = cardWidth + gap;

   function updateCarouselPosition() {
       storyCarousel.style.transform = `translateX(-${moveDistance * currentIndex}px)`;
   }

   previousButton.addEventListener('click',e=>{
    if(currentIndex>0){
        currentIndex--;
        updateCarouselPosition();
    }
   })
   nextButton.addEventListener('click',e=>{
    if(currentIndex<storyCards.length-1){
        currentIndex++;
        updateCarouselPosition();
    }
   })
    profileAvatar.addEventListener('click',(e)=>{
        console.log(e.currentTarget.dataset.toggle,"e.currentTarget");
       let menu=document.querySelector(e.currentTarget.dataset.toggle);
       menu.classList.toggle('open');
    })
})