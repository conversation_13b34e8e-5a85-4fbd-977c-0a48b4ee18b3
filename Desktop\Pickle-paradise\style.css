@import url('https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&family=Roboto:ital,wght@0,100..900;1,100..900&display=swap');

*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Lato', sans-serif;
}
:root{
    --primary-color: #1877f2;
    --secondary-color: #f0f2f5;
    --tertiary-color: #fff;
    --quaternary-color: #e9ebee;
    --text-color: #333;
}
nav{
    height: 60px;
    background-color: var(--primary-color);
    color: var(--secondary-color);
    width: 100vw;
    display: flex;
    position: sticky;
    top: 0;
    z-index: 10;
    justify-content: space-between;
    align-items: center;
    padding: 5px 5%;
}
.color-primary{
    color: var(--primary-color);
}
.left-nav ,.right-nav{
    display: flex;
    align-items: center;
    flex: 1;
    gap: 20px;
}
.right-nav{
    justify-content: flex-end;
}
.nav-icon{
    display: flex;
    align-items: center;
    gap: 20px;
}
.logo{
    height: 40px;
    cursor: pointer;
}
.icon-image{
    width: 20px;
    cursor: pointer;
}
.avatar-image{
    width: 30px;
    cursor: pointer;
    border-radius: 50%;
}

.input-search{
    height: 40px;
    padding: 0 10px;
    border-radius: 20px;
    border: 1px solid var(--quaternary-color);
    flex: 0.8;
    display: flex;
    align-items: center;
    gap: 10px;
}
.input-search input{
    flex: 1;
    width: 100%;
    height: 100%;
    color: var(--secondary-color);
    font-size: 14px;
    padding: 10px 20px;
    border: none;
    outline: none;
    background: transparent;
}
.input-search input::placeholder{
    color: var(--secondary-color);
}
.profile-avatar{
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}
.profile-avatar::before{
    content: '';
    position: absolute;
    width: 8px;
    height: 8px;
    bottom: 0;
    left: 50%;
    border-radius: 50%;
    background-color: green;
    transform: translate(-50%, 50%);
    border: 1px solid var(--tertiary-color);

}

.main-body{
    width: 100vw;
    display: flex;
}
aside{
    flex: 0.25;
    height: calc(100vh - 60px);
    overflow-x: hidden;
    position: sticky;
    display: flex;
    flex-direction: column;
    gap: 15px;
    top: 60px;
    color: var(--text-color);
    overflow-y: hidden;
    background-color: var(--secondary-color);
}
.link-attcahment ,.shortcut-links{
    overflow-x: hidden;
    overflow-y: hidden;
    padding: 0 10px;
}
.link-attcahment ul ,.shortcut-links ul{
   width: 100%;
   height: 100%;
   list-style: none;
   padding: 0;
   margin: 0;
}
.link-attcahment ul li ,.shortcut-links ul li{
    padding: 10px 20px;
    cursor: pointer;
    display: flex;
    color: var(--text-color);
    align-items: center;
    gap: 20px;
    justify-content: flex-start;
}
.link-attcahment ul:last-child{
    font-size: 12px;
    color: var(--primary-color);
}
aside hr{
    border: 1px solid var(--text-color);
    margin: 0;
}
main{
    flex: 0.5;
}
.right-aside{
    flex: 0.25;
}

.event-anchor{
    display: flex;
    gap: 20px;
    padding: 10px 20px;
    cursor: pointer;
    align-items: flex-start;
    justify-content: flex-start;
    color: var(--text-color);
}
.event-no-box{
    display: flex;
    flex-direction: column;
    gap: 5px;
    height: 100px;
    border-radius: 10px;
    align-items: center;
    justify-content: space-around;
    flex: 0.3 !important;
    background-color: var(--tertiary-color);
    color: var(--text-color);
}
.event-no-box>span:first-child{
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex: 0.8;
}
.event-no-box>span:last-child{
    flex: 0.3;
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: center;
    border-radius: 0 0 12px 12px;
    background-color: var(--primary-color);
    color: var(--tertiary-color);
}
.event-details{
    flex: 0.7;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    gap: 5px;
    color: var(--text-color);
}
.event-details > p{
    display: flex;
    align-items: center;
    gap: 10px;
}
.small-text{
    font-size: 10px;
    color: var(--primary-color);
    font-weight: 600;
}
.small-icon,.small-image{
    width: 10px;
}
.sidebar-nav{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    color: var(--text-color);
}
p{
    padding: 10px 0;
}
.width-100-image{
    width: 100%;
    height: 100%;
    border: 1px solid var(--quaternary-color);
    object-fit: cover;
}
.advertisment{
    display: flex;
    flex: 0.4;
    flex-direction: column;
    gap: 10px;
}
.Conversation{
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 10px;
}
.Conversation ul li{
    display: flex;
    align-items: center;
    padding: 10px 20px;
    cursor: pointer;
    color: var(--text-color);
}
.main-content{
    overflow-y: scroll;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 2rem;
    flex: 0.5;
}
.carousel-holder{
    overflow: hidden;   
    position: relative;
}
.story-carousel{
    height: 300px;
    padding: 0 30px;
    display: flex;
 
    gap: 10px;
} 
.previous-button ,.next-button{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--tertiary-color);
    cursor: pointer;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
}
.next-button{
    right: 0;
}
.previous-button>i ,.next-button>i{
    color: var(--primary-color);
}
.story-carousel::-webkit-scrollbar{
    display: none;
}
.story-card{
    height: 100%;
    flex-basis: 160px;
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
    border-radius: 10px;
    flex-shrink: 0;
    border: 1px solid var(--quaternary-color);
    position: relative;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
.profile-head{
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 20px;
    border-bottom: 1px solid var(--quaternary-color);
}
.profile-menu{
    display: flex;
    flex-direction: column;
    padding: 0;
    gap: 10px;
    background-color: var(--secondary-color);
    position: absolute;
    top: 60px;
    right: 0;
    width: 300px;
    height: 0px;overflow: hidden;
    z-index: 10;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}
.profile-menu.open{
    height: 40vh;
    padding: 10px 20px;
}
.full-width-image{
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.story-profile-image{
    position: absolute;
    top: 10px;
    left: 10px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid var(--tertiary-color);
    object-fit: cover;
}
.story-name{
    position:absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    color: var(--tertiary-color);
    font-weight: 600;
}
#add-story{
    position: absolute;
    top: 50%;
    left: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: var(--primary-color);
    border: 2px solid var(--tertiary-color);
    transform: translate(-50%, -50%);
    font-size: 20px;
    color: var(--tertiary-color);
    cursor: pointer;
}
.overlay{
    position:absolute;
    top: 0;
    left: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
}

.myPostUpload{
    display: flex;
    flex-direction: column;
    gap: 10px;
    border-radius: 10px;
    background-color: var(--secondary-color);
    padding: 20px 30px;
}
.postHeader{
    padding: 20px 10px;
    display: flex;
    align-items: center;
    gap: 10px;
    width: max-content;
    align-content: center;
    justify-content: space-between;
}
.profile-details{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    color: var(--text-color);
    text-align: center;
}
.menu-items ul{
 display: flex;
 flex-direction: column;
 gap: 10px;
 padding: 10px 20px;
}
.menu-item{
    display: flex;
    color: var(--text-color);
    align-items: center;
    gap: 15px;
    cursor: pointer;
}
.menu-item>i:last-child{
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
}
.post-nav{
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0px;
}
textarea{
    width: 100%;
    height: 100px;
    border: none;
    outline: none;
    resize: none;
    font-size: 14px;
    color: var(--text-color);
    padding: 10px 30px;
    border-radius: 10px;
    background-color: var(--tertiary-color);
    border: 1px solid var(--quaternary-color);
}
.others-posts{
    padding: 0 30px;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    background-color: var(--secondary-color);
}
.uploadOptions{
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 30px;
}